import 'dart:async';
import 'dart:io';

import 'package:base_app/core/constants/app_constants.dart';
import 'package:flutter/foundation.dart';

class AppHelper {
  AppHelper._();

  static final AppHelper instance = AppHelper._();

  Future<bool> checkInternetConnection() async {
    try {
      final result =
          await InternetAddress.lookup(AppConstants.baseGoogle).timeout(
        const Duration(
          seconds: 3, // Giảm timeout để phản hồi nhanh hơn
        ),
      );
      print("result: $result");
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      // Không có kết nối internet
      return false;
    } catch (e) {
      // Log lỗi khác để debug
      debugPrint('Error checking internet connection: $e');
      return false;
    }
  }
}
