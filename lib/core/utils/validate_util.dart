import 'package:base_app/core/translation/app_string.dart';

enum ValidationType {
  email,
  phone,
  password,
  point,
  user,
}

class ValidateUtil {
  static ValidateUtil instance = ValidateUtil();

  static ValidateUtil getInstance() {
    return instance;
  }

  bool isEmail(String em) {
    String p =
        r"^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9][a-zA-Z0-9-]{0,253}\.)*[a-zA-Z0-9][a-zA-Z0-9-]{0,253}\.[a-zA-Z0-9]{2,}$";
    RegExp regExp = RegExp(p);
    return regExp.hasMatch(em);
  }

  bool isPhone(String em) {
    String p = r"^(?:\+84|0)(3[2-9]|5[6,8,9]|7[0,6-9]|8[1-9]|9[0-9])[0-9]{7}$";
    RegExp regExp = RegExp(p);
    return regExp.hasMatch(em);
  }

  String? validateRequiredField({
    dynamic value,
    required bool isRequired,
    required ValidationType type,
    double? maxPoint,
  }) {
    bool isEmpty = value == null || value.toString().trim().isEmpty;

    if (isEmpty) {
      if (isRequired) {
        return switch (type) {
          ValidationType.point => AppString.enterPoint,
          ValidationType.user => AppString.enterChooseUser,
          ValidationType.email => AppString.enterEmail,
          ValidationType.phone => AppString.enterPhone,
          ValidationType.password => AppString.enterPassword,
        };
      }
      return null;
    }

    switch (type) {
      case ValidationType.email:
        if (!ValidateUtil.instance.isEmail(value.toString())) {
          return AppString.invalidEmail;
        }
        break;

      case ValidationType.phone:
        if (!ValidateUtil.instance.isPhone(value.toString())) {
          return AppString.enterValidPhone;
        }
        break;

      case ValidationType.point:
        double? pointValue = double.tryParse(value.toString());
        if (pointValue == null) {
          return AppString.invalidPoint;
        }

        if (pointValue < 0) {
          return AppString.pointMustBePositive;
        }

        double remainder = (pointValue * 4) % 1;
        if (remainder != 0) {
          return AppString.onlyAcceptSpecificValues;
        }

        if (maxPoint != null && pointValue > maxPoint) {
          return AppString.pointCannotExceedMaximum(maxPoint.toString());
        }
        break;

      case ValidationType.password:
        break;

      case ValidationType.user:
        break;
    }

    return null;
  }
}
