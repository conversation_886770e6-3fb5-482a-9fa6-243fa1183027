import 'package:base_app/core/error/app_failures.dart';
import 'package:dio/dio.dart';
import 'package:fpdart/fpdart.dart';

typedef AppTaskEither<T> = TaskEither<AppFailures, T>;

class TaskEitherUtils {
  TaskEitherUtils._();

  static AppTaskEither<T> fromFuture<T>(Future<T> future) {
    return TaskEither.tryCatch(
      () async {
        return await future;
      },
      (error, stackTrace) => _handleError(error, stackTrace),
    );
  }

  static AppTaskEither<T> fromAsync<T>(Future<T> Function() asyncFn) {
    return TaskEither.tryCatch(
      () async {
        return await asyncFn();
      },
      (error, stackTrace) => _handleError(error, stackTrace),
    );
  }

  static AppTaskEither<T> success<T>(T value) {
    return TaskEither.right(value);
  }

  static AppTaskEither<T> failure<T>(AppFailures failure) {
    return TaskEither.left(failure);
  }

  static AppTaskEither<T> fromEither<T>(Either<AppFailures, T> either) {
    return TaskEither.fromEither(either);
  }

  static AppTaskEither<C> chain3<A, B, C>(
    AppTaskEither<A> taskA,
    AppTaskEither<B> Function(A) taskB,
    AppTaskEither<C> Function(B) taskC,
  ) {
    return taskA.flatMap((a) => taskB(a).flatMap((b) => taskC(b)));
  }

  static AppTaskEither<(A, B)> combine2<A, B>(
    AppTaskEither<A> taskA,
    AppTaskEither<B> taskB,
  ) {
    return taskA.flatMap((a) => taskB.map((b) => (a, b)));
  }

  static AppTaskEither<(A, B, C)> combine3<A, B, C>(
    AppTaskEither<A> taskA,
    AppTaskEither<B> taskB,
    AppTaskEither<C> taskC,
  ) {
    return taskA
        .flatMap((a) => taskB.flatMap((b) => taskC.map((c) => (a, b, c))));
  }

  static AppTaskEither<List<T>> sequence<T>(List<AppTaskEither<T>> tasks) {
    return tasks.fold<AppTaskEither<List<T>>>(
      success(<T>[]),
      (acc, task) =>
          combine2(acc, task).map((tuple) => [...tuple.$1, tuple.$2]),
    );
  }

  static AppTaskEither<List<T>> parallel<T>(List<AppTaskEither<T>> tasks) {
    return TaskEither.tryCatch(
      () async {
        final futures = tasks.map((task) => task.run()).toList();
        final results = await Future.wait(futures);

        final failures = results.where((r) => r.isLeft()).toList();
        if (failures.isNotEmpty) {
          throw failures.first.getLeft().toNullable()!;
        }

        return results.map((r) => r.getRight().toNullable()!).toList();
      },
      (error, stackTrace) => _handleError(error, stackTrace),
    );
  }

  static AppTaskEither<T> retry<T>(
    AppTaskEither<T> Function() taskFn, {
    int maxRetries = 3,
    Duration delay = const Duration(milliseconds: 500),
  }) {
    return TaskEither.tryCatch(
      () async {
        for (int i = 0; i < maxRetries; i++) {
          final result = await taskFn().run();
          if (result.isRight()) {
            return result.getRight().toNullable() as T;
          }

          if (i < maxRetries - 1) {
            await Future.delayed(delay);
          }
        }

        final finalResult = await taskFn().run();
        throw finalResult.getLeft().toNullable()!;
      },
      (error, stackTrace) => _handleError(error, stackTrace),
    );
  }

  static AppTaskEither<T> timeout<T>(
    AppTaskEither<T> task, {
    required Duration duration,
    String? timeoutMessage,
  }) {
    return TaskEither.tryCatch(
      () async {
        final result = await task.run().timeout(
              duration,
              onTimeout: () => Either.left(
                NetworkFailure(
                  message: timeoutMessage ??
                      'Operation timed out after ${duration.inSeconds}s',
                ),
              ),
            );

        if (result.isRight()) {
          final value = result.getRight().toNullable();
          if (value != null) {
            return value;
          } else {
            throw const UnknownFailure(message: 'Unexpected null value');
          }
        } else {
          final failure = result.getLeft().toNullable();
          if (failure != null) {
            throw failure;
          } else {
            throw const UnknownFailure(message: 'Unexpected null failure');
          }
        }
      },
      (error, stackTrace) => _handleError(error, stackTrace),
    );
  }

  static AppTaskEither<T> mapError<T>(
    AppTaskEither<T> task,
    AppFailures Function(AppFailures) errorMapper,
  ) {
    return task.mapLeft(errorMapper);
  }

  static AppFailures _handleError(Object error, StackTrace stackTrace) {
    if (error is AppFailures) {
      return error;
    }

    if (error is DioException) {
      return _handleDioError(error);
    }

    final errorMessage = error.toString();

    if (errorMessage.contains('SocketException') ||
        errorMessage.contains('NetworkException') ||
        errorMessage.contains('No Internet')) {
      return NetworkFailure(
        message: 'Network connection failed',
      );
    }

    if (errorMessage.contains('TimeoutException')) {
      return NetworkFailure(
        message: 'Request timeout',
      );
    }

    return UnknownFailure(
      message: 'An unexpected error occurred: $errorMessage',
    );
  }

  static AppFailures _handleDioError(DioException dioError) {
    switch (dioError.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkFailure(
          message: 'Request timeout',
        );

      case DioExceptionType.connectionError:
        return NetworkFailure(
          message: 'Network connection failed',
        );

      case DioExceptionType.badResponse:
        return ServerFailure(
          message:
              dioError.response?.data['message'] ?? 'Server error occurred',
          statusCode: dioError.response?.statusCode,
        );

      case DioExceptionType.cancel:
        return NetworkFailure(
          message: 'Request was cancelled',
        );

      case DioExceptionType.unknown:
      default:
        return UnknownFailure(
          message: 'Network error: ${dioError.message}',
        );
    }
  }
}

class ResponseData {
  final bool success;
  final dynamic message;
  final dynamic data;

  ResponseData({
    required this.success,
    required this.message,
    this.data,
  });

  factory ResponseData.fromJson(Map<String, dynamic> json) {
    return ResponseData(
      success: json['success'] as bool,
      message: json['message'],
      data: json['data'],
    );
  }
}
