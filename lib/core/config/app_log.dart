import 'package:logger/logger.dart';

class AppLog {
  AppLog._();
  static final Logger logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      levelEmojis: {
        Level.debug: '🌈',
        Level.info: '🌸',
        Level.warning: '🟡',
        Level.error: '🔴',
      },
      colors: false,
      dateTimeFormat: (time) => '${time.hour}:${time.minute}:${time.second}',
    ),
  );
}
