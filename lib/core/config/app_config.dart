import 'dart:io';

import 'package:base_app/core/config/app_log.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

enum Environment {
  dev,
  prod;
}

class AppConfig {
  static late Environment env;
  static late BaseDeviceInfo deviceInfo;
  static late PackageInfo packageInfo;
  static late int androidVersion;

  static Future<void> initialConfig() async {
    String? flavor;
    try {
      flavor = await const MethodChannel('main_channel')
          .invokeMethod<String>('getFlavor');
    } catch (_) {}
    env = Environment.values.firstWhere(
      (element) => element.name == flavor,
      orElse: () => Environment.dev,
    );

    packageInfo = await PackageInfo.fromPlatform();

    if (Platform.isAndroid) {
      deviceInfo = await DeviceInfoPlugin().androidInfo;
      final version = deviceInfo.data["version"]["release"] as String;
      if (version.contains('.')) {
        androidVersion = int.parse(version.split('.').first);
      } else {
        androidVersion = int.parse(version);
      }
    } else if (Platform.isIOS) {
      deviceInfo = await DeviceInfoPlugin().iosInfo;
    } else {
      deviceInfo = BaseDeviceInfo({});
    }

    AppLog.logger
        .i('Info: ${packageInfo.appName} | ${packageInfo.packageName} | $env');
  }
}
