// ignore_for_file: constant_identifier_names

import 'dart:convert';

import 'package:base_app/features/login/data/models/user_model.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'i_local_storage_service.dart';
import 'local_storage.dart';

@LazySingleton(as: ILocalStorageService)
class LocalStorageService extends ILocalStorageService {
  static const PREF_ACCESS_TOKEN = 'PREF_ACCESS_TOKEN';
  static const PREF_REF_TOKEN = 'PREF_REF_TOKEN';
  static const PREF_LANGUAGE = 'PREF_LANGUAGE';
  static const PREF_LOGGED_ACCOUNT = 'PREF_LOGGED_ACCOUNT';
  static const PREF_FCM_TOKEN = 'PREF_FCM_TOKEN';
  static const PREF_USER_PHONE = 'PREF_USER_PHONE';
  static const PREF_REMEMBER_PHONE = 'PREF_REMEMBER_PHONE';
  static const PREF_PHONE_REMEMBER = 'PREF_PHONE_REMEMBER';

  final SharedPreferences _preferences = LocalStorage.instant.preferences;

  @override
  Future<bool> hasAuthenticated() async {
    String accessToken = await getAccessToken();
    return accessToken.isNotEmpty;
  }

  @override
  Future setAccessToken(String accessToken) async {
    await _preferences.setString(PREF_ACCESS_TOKEN, accessToken);
  }

  @override
  Future<String> getAccessToken() async {
    return _preferences.getString(PREF_ACCESS_TOKEN) ?? '';
  }

  @override
  Future clearAll() async {
    await _preferences.remove(PREF_ACCESS_TOKEN);
    await _preferences.clear();
    return true;
  }

  @override
  Future<String> getRefreshToken() async {
    return _preferences.getString(PREF_REF_TOKEN) ?? '';
  }

  @override
  Future setRefreshToken(String refreshToken) async {
    await _preferences.setString(PREF_REF_TOKEN, refreshToken);
  }

  @override
  Future saveCredentials(String accessToken, UserModel user) async {
    await Future.wait([
      setAccessToken(accessToken),
      setLoggedUser(user),
    ]);
  }

  @override
  Future setLoggedUser(UserModel user) async {
    await _preferences.setString(PREF_LOGGED_ACCOUNT, jsonEncode(user));
  }

  @override
  Future setFCMToken(String fcmToken) async {
    await _preferences.setString(PREF_FCM_TOKEN, fcmToken);
  }

  @override
  Future<String> getFCMToken() async {
    return _preferences.getString(PREF_FCM_TOKEN) ?? '';
  }

  @override
  Future setUserPhone(String phone) async {
    await _preferences.setString(PREF_USER_PHONE, phone);
  }

  @override
  Future<String> getUserPhone() async {
    return _preferences.getString(PREF_USER_PHONE) ?? '';
  }

  @override
  Future<bool> setRememberPhone(bool value) async {
    return _preferences.setBool(PREF_REMEMBER_PHONE, value);  
  }

  @override
  Future<bool> getRememberPhone() async {
    return _preferences.getBool(PREF_REMEMBER_PHONE) ?? false;
  }

  @override
  Future setPhoneNumerRemember(String phone) async {
    await _preferences.setString(PREF_PHONE_REMEMBER, phone);
  }

  @override
  Future<String> getPhoneNumerRemember() async {
    return _preferences.getString(PREF_PHONE_REMEMBER) ?? '';
  }
}
