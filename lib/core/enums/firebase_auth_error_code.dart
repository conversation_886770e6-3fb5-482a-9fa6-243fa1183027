enum FirebaseAuthErrorCode {
  invalidPhoneNumber('invalid-phone-number'),
  tooManyRequests('too-many-requests'),
  webContextCancelled('web-context-cancelled'),
  invalidVerificationCode('invalid-verification-code'),
  sessionExpired('session-expired'),
  invalidVerificationId('invalid-verification-id'),
  networkRequestFailed('network-request-failed'),
  unknown('');

  final String code;
  const FirebaseAuthErrorCode(this.code);

  static FirebaseAuthErrorCode fromCode(String code) {
    return FirebaseAuthErrorCode.values.firstWhere(
      (e) => e.code == code,
      orElse: () => FirebaseAuthErrorCode.unknown,
    );
  }
}
