import 'package:base_app/core/config/app_log.dart';
import 'package:base_app/core/constants/api_constants.dart';
import 'package:dio/dio.dart';

class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    AppLog.logger.i(
        'REQUEST: 🌤️ [${options.method}] => PATH: ${ApiConstants.baseUrl}${options.path}');
    if (options.data != null) {
      AppLog.logger.d('Data: ${options.data}');
    }
    if (options.queryParameters.isNotEmpty) {
      AppLog.logger.d('Query Parameters: ${options.queryParameters}');
    }

    return super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    AppLog.logger.i('RESPONSE: 🌈 [${response.statusCode}] ');
    AppLog.logger.i('Response Data: ${response.data}');

    return super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppLog.logger.e(
        'ERROR: 🌺 [${err.response?.statusCode}] => PATH: ${err.requestOptions.path}');
    AppLog.logger.e('Error Message: ${err.message}');
    if (err.response?.data != null) {
      AppLog.logger.e('Error Data: ${err.response?.data}');
    }

    return super.onError(err, handler);
  }
}
