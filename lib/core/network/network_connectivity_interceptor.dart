import 'package:base_app/core/helper/app_hepler.dart';
import 'package:base_app/core/utils/dialog_util.dart';
import 'package:base_app/core/utils/loading_overlay_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class NetworkConnectivityInterceptor extends Interceptor {
  final GlobalKey<NavigatorState> navigatorKey;

  bool _isDialogShowing = false;

  NetworkConnectivityInterceptor({
    required this.navigatorKey,
  });

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final isConnected = await AppHelper.instance.checkInternetConnection();

    if (!isConnected) {
      if (!_isDialogShowing) {
        await _showNoInternetDialog();
      }

      handler.reject(
        DioException(
          requestOptions: options,
          type: DioExceptionType.connectionError,
          message: 'No internet connection',
        ),
      );
      return;
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.type == DioExceptionType.connectionError ||
        err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.sendTimeout) {
      final isConnected = await AppHelper.instance.checkInternetConnection();

      if (!isConnected && !_isDialogShowing) {
        await _showNoInternetDialog();
      }
    }

    handler.next(err);
  }

  Future<void> _showNoInternetDialog() async {
    AppLoadingOverlay.hide();
    _isDialogShowing = true;

    final context = navigatorKey.currentContext;
    if (context == null) {
      _isDialogShowing = false;
      return;
    }

    try {
      await DialogUtil.showNoInternetDialog(
        context,
        onSubmit: () {
          if (context.mounted) {
            context.pop();
          }
        },
      );
    } catch (e) {
      debugPrint('Error showing no internet dialog: $e');
    } finally {
      _isDialogShowing = false;
    }
  }
}
