import 'package:flutter/material.dart';

import 'app_colors.dart';

class AppTextStyle {
  static const double _fontSizeScale = 0;

  static TextStyle bold([double fontSize = 16]) {
    fontSize += _fontSizeScale;
    return TextStyle(
        fontWeight: FontWeight.w700,
        fontSize: fontSize,
        color: AppColors.primaryDark,
        fontFamily: 'Roboto',
        height: 1);
  }

  static TextStyle semibold([double fontSize = 16]) {
    fontSize += _fontSizeScale;
    return TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: fontSize,
        color: AppColors.primaryDark,
        fontFamily: 'Roboto',
        height: 1);
  }

  static TextStyle medium([double fontSize = 16]) {
    fontSize += _fontSizeScale;
    return TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: fontSize,
        color: AppColors.primaryDark,
        fontFamily: 'Roboto',
        height: 1);
  }

  static TextStyle regular([double fontSize = 16]) {
    fontSize += _fontSizeScale;
    return TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w400,
        color: AppColors.primaryDark,
        fontFamily: 'Roboto',
        height: 1);
  }
}
