// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i361;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../../features/login/cubit/login_cubit.dart' as _i209;
import '../../features/login/data/datasources/login_remote_datasource.dart'
    as _i1033;
import '../../features/login/domain/repositories/login_repository.dart'
    as _i902;
import '../../features/otp/cubit/otp_cubit.dart' as _i420;
import '../../features/otp/data/datasources/otp_remote_datasource.dart'
    as _i535;
import '../../features/otp/domain/repositories/otp_repository.dart' as _i573;
import '../local_storage/i_local_storage_service.dart' as _i316;
import '../local_storage/local_storage_service.dart' as _i71;
import '../network/dio_client.dart' as _i667;
import 'injection_module.dart' as _i212;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final injectionModule = _$InjectionModule();
    gh.factory<_i420.OtpCubit>(() => _i420.OtpCubit());
    gh.lazySingleton<_i667.DioClient>(() => _i667.DioClient());
    gh.lazySingleton<_i209.LoginCubit>(() => _i209.LoginCubit());
    gh.lazySingleton<_i316.ILocalStorageService>(
        () => _i71.LocalStorageService());
    gh.lazySingleton<_i361.Dio>(
        () => injectionModule.dio(gh<_i667.DioClient>()));
    gh.lazySingleton<_i535.OtpRemoteDataSource>(
        () => _i535.OtpRemoteDataSource(gh<_i361.Dio>()));
    gh.lazySingleton<_i1033.LoginRemoteDataSource>(
        () => _i1033.LoginRemoteDataSource(gh<_i361.Dio>()));
    gh.lazySingleton<_i902.LoginRepository>(
        () => _i902.LoginRepository(gh<_i1033.LoginRemoteDataSource>()));
    gh.lazySingleton<_i573.OtpRepository>(
        () => _i573.OtpRepository(gh<_i535.OtpRemoteDataSource>()));
    return this;
  }
}

class _$InjectionModule extends _i212.InjectionModule {}
