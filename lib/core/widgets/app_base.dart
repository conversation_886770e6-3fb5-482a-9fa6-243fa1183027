import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AppBase extends StatelessWidget {
  final Widget body;
  final bool showAppBar;
  final String? titleAppBar;
  final TextStyle? titleStyle;
  final Widget? titleWidget;
  final bool? centerTitle;
  final Color? backgroundColorAppBar;
  final Color? backgroundColor;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? colorIconBack;
  final bool showBackButton;
  const AppBase({
    super.key,
    required this.body,
    this.showAppBar = true,
    this.centerTitle = true,
    this.backgroundColorAppBar = AppColors.primary,
    this.backgroundColor,
    this.titleAppBar,
    this.titleWidget,
    this.actions,
    this.leading,
    this.titleStyle,
    this.colorIconBack = AppColors.white,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor ?? AppColors.white,
      appBar: showAppBar
          ? AppBar(
              title: titleAppBar != null
                  ? Text(
                      titleAppBar!,
                      style: titleStyle ??
                          AppTextStyle.bold(18)
                              .copyWith(color: AppColors.white),
                    )
                  : titleWidget,
              centerTitle: centerTitle,
              backgroundColor: backgroundColorAppBar,
              scrolledUnderElevation: 0,
              elevation: 0,
              actions: actions,
              leading: leading ??
                  (showBackButton
                      ? IconButton(
                          icon: Icon(
                            Icons.adaptive.arrow_back,
                            color: colorIconBack,
                          ),
                          onPressed: () => context.pop(),
                        )
                      : null),
            )
          : null,
      body: body,
    );
  }
}
