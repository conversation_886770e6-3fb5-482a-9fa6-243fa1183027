import 'package:flutter/material.dart';

class AppTextFormField extends StatelessWidget {
  final TextEditingController controller;
  final String? labelText;
  final String? hintText;
  final String? initialValue;
  final String? Function(String?)? validator;
  final Widget? suffixIcon;
  final bool isSearching;
  final Function(String)? onChanged;
  final Function()? onClear;

  const AppTextFormField({
    super.key,
    required this.controller,
    this.labelText,
    this.hintText,
    this.onChanged,
    this.suffixIcon,
    this.validator,
    this.initialValue,
    this.isSearching = false,
    this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: initialValue,
      controller: controller,
      // <PERSON>hi lần đầu vào không bị validator, chọn mode AutovalidateMode.onUserInteraction
      autovalidateMode: AutovalidateMode.onUserInteraction,
      onChanged: onChanged,
      validator: validator,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        suffixIcon: suffixIcon ??
            IconButton(
              onPressed: !isSearching ? null : onClear,
              icon: !isSearching || controller.text.isEmpty
                  ? const Icon(Icons.search)
                  : const Icon(Icons.clear),
            ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            color: Colors.green,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            color: Colors.blue,
            width: 1,
          ),
        ),
      ),
    );
  }
}
