// Enum định nghĩa loại SnackBar
import 'dart:io';

import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:flutter/material.dart';

enum SnackBarType { success, warning, error, info }

extension SnackBarTypeExtension on SnackBarType {
  Color get color {
    switch (this) {
      case SnackBarType.success:
        return Colors.green;
      case SnackBarType.warning:
        return Colors.orange;
      case SnackBarType.error:
        return Colors.red;
      case SnackBarType.info:
        return Colors.blue;
    }
  }

  IconData get icon {
    switch (this) {
      case SnackBarType.success:
        return Icons.check_circle;
      case SnackBarType.warning:
        return Icons.warning;
      case SnackBarType.error:
        return Icons.error;
      case SnackBarType.info:
        return Icons.info;
    }
  }
}

class AppSnackBar {
  static void showCustom(
    BuildContext context,
    String? message,
    SnackBarType type, {
    Duration duration = const Duration(seconds: 3),
    bool showInBottom = false,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: type.color,
        duration: duration,
        margin: EdgeInsets.only(
          bottom: showInBottom
              ? 0
              : context.screenSize.height -
                  context.dynamicHeight(Platform.isIOS ? 0.15 : 0.12),
          left: 16,
          right: 16,
        ),
        behavior: SnackBarBehavior.floating,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(10),
          ),
        ),
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(type.icon, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message ?? AppString.defaultError,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void success(
    BuildContext context, {
    String? message,
    bool showInBottom = false,
  }) =>
      showCustom(
        context,
        message,
        SnackBarType.success,
        showInBottom: showInBottom,
      );

  static void warning(
    BuildContext context, {
    String? message,
    bool showInBottom = false,
  }) =>
      showCustom(
        context,
        message,
        SnackBarType.warning,
        showInBottom: showInBottom,
      );

  static void error(
    BuildContext context, {
    String? message,
    bool showInBottom = false,
  }) =>
      showCustom(
        context,
        message,
        SnackBarType.error,
        showInBottom: showInBottom,
      );

  static void info(
    BuildContext context, {
    String? message,
    bool showInBottom = false,
  }) =>
      showCustom(
        context,
        message,
        SnackBarType.info,
        showInBottom: showInBottom,
      );
}
