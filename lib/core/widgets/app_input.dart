import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';


class BaseInput extends StatelessWidget {
  const BaseInput({
    super.key,
    this.label,
    this.controller,
    this.onChanged,
    this.focusNode,
    this.keyboardType = TextInputType.text,
    this.isArea = false,
    this.isRequired = false,
    this.validator,
    this.initialValue,
    this.disable = false,
    this.shouldHaveLabel = true,
    this.outlineBorderColor,
    this.hintStyle,
    this.inputStyle,
    this.hintText,
    this.cursorColor,
    this.shouldHaveBorder = true,
    this.obscureText = false,
    this.note,
    this.prefixIcon,
    this.suffixIcon,
    this.height,
    this.shouldLabel = true,
    this.maxLines,
    this.textInputAction = TextInputAction.done,
    this.fillColor = AppColors.white,
    this.inputFormatters,
    this.padding,
    this.textAlign,
    this.submitted = false,
    this.onFocus,
    this.readOnly = false,
    this.errorText,
    this.colorBorderSideDis,
  });

  final TextEditingController? controller;
  final Function(String)? onChanged;
  final FocusNode? focusNode;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final bool isArea;
  final bool isRequired;
  final bool disable;
  final String? label;
  final String? initialValue;
  final bool shouldHaveLabel;
  final Color? outlineBorderColor;
  final TextStyle? inputStyle;
  final TextStyle? hintStyle;
  final String? hintText;
  final Color? cursorColor;
  final bool shouldHaveBorder;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Color? fillColor;
  final String? note;
  final bool obscureText;
  final double? height;
  final bool shouldLabel;
  final List<TextInputFormatter>? inputFormatters;
  final EdgeInsets? padding;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextInputAction textInputAction;
  final bool submitted;
  final Function(bool)? onFocus;
  final bool readOnly;
  final String? errorText;
  final Color? colorBorderSideDis;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        shouldLabel
            ? Row(
                children: [
                  shouldHaveLabel
                      ? Flexible(
                          child: RichText(
                              text: TextSpan(children: [
                            TextSpan(
                                text: label,
                                style: AppTextStyle.medium()
                                    .copyWith(color: AppColors.black)),
                            isRequired
                                ? TextSpan(
                                    text: ' *',
                                    style: AppTextStyle.medium()
                                        .copyWith(color: AppColors.red))
                                : const TextSpan()
                          ])),
                        )
                      : const SizedBox(),
                  Text(note ?? '',
                      style: AppTextStyle.medium().copyWith(color: AppColors.black))
                ],
              )
            : SizedBox(),
        SizedBox(height: shouldHaveLabel ? context.appPadding3 : 0),
        FormField<String>(
            validator: validator,
            builder: (FormFieldState field) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: isArea ? 200 : height,
                    child: TextFormField(
                      readOnly: readOnly,
                      initialValue: initialValue,
                      controller: controller,
                      enabled: !disable,
                      keyboardType: keyboardType,
                      expands: (maxLines ?? 0) > 1 ? true : false,
                      maxLines: maxLines,
                      minLines: null,
                      textInputAction: textInputAction,
                      focusNode: focusNode,
                      obscureText: obscureText,
                      onTap: () {
                        onFocus?.call(true);
                      },
                      onChanged: (String value) {
                        field.didChange(value);
                        onChanged?.call(value);
                      },
                      inputFormatters: inputFormatters,
                      textAlign: textAlign ?? TextAlign.start,
                      style: inputStyle ??
                          AppTextStyle.medium().copyWith(
                            color: disable ? AppColors.gray600 : null,
                          ),
                      decoration: InputDecoration(
                        errorText: errorText,
                        filled: fillColor != null,
                        contentPadding: padding ??
                            EdgeInsets.symmetric(
                                horizontal: context.appPadding, vertical: 10.0),
                        prefixIcon: prefixIcon,
                        suffixIcon: suffixIcon,
                        fillColor: disable ? AppColors.gray : fillColor,
                        hintText: hintText ?? '',
                        hintStyle: hintStyle ??
                            AppTextStyle.medium()
                                .copyWith(color: AppColors.taupeGray),
                        enabledBorder: shouldHaveBorder
                            ? OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.0),
                                borderSide: BorderSide(
                                    color: !submitted || field.errorText == null
                                        ? AppColors.black
                                        : AppColors.red),
                              )
                            : InputBorder.none,
                        disabledBorder: shouldHaveBorder
                            ? OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.0),
                                borderSide: BorderSide(
                                    color: colorBorderSideDis ??
                                        AppColors.gray400),
                              )
                            : InputBorder.none,
                        border: shouldHaveBorder
                            ? OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.0),
                                borderSide:
                                    const BorderSide(color: AppColors.black),
                              )
                            : InputBorder.none,
                        focusedBorder: shouldHaveBorder
                            ? OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.0),
                                borderSide: BorderSide(
                                    color: outlineBorderColor ??
                                        AppColors.honoluluBlue),
                              )
                            : InputBorder.none,
                        errorMaxLines: 1,
                      ),
                      cursorColor: cursorColor ?? AppColors.honoluluBlue,
                    ),
                  ),
                  if (submitted && field.errorText != null)
                    Container(
                      padding: const EdgeInsets.only(top: 8, left: 12),
                      child: Text(field.errorText!,
                          style: AppTextStyle.regular(13).copyWith(
                              color: Theme.of(context).colorScheme.error)),
                    ),
                ],
              );
            }),
      ],
    );
  }
}
