import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class AppError extends StatelessWidget {
  const AppError({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBase(
      showAppBar: false,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppString.defaultError,
              style: AppTextStyle.medium(),
            ),
            Lottie.asset(
              Assets.lotties.a404Error,
              width: context.width,
              height: context.height * 0.6,
              fit: BoxFit.cover,
            ),
          ],
        ),
      ),
    );
  }
}
