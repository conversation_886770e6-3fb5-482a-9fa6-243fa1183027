import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';

enum ButtonType { primary, secondary, outline }

class ButtonStyle {
  final Color bg;
  final Color titleColor;

  ButtonStyle({
    required this.bg,
    required this.titleColor,
  });
}

class AppButton extends StatelessWidget {
  final String? title;
  final Function? onSubmit;
  final Color? backgroundColor;
  final Color? titleColor;
  final double titleSize;
  final Color? borderColor;
  final double height;
  final double? width;
  final TextStyle? titleStyle;
  final bool isDisable;
  final bool intrinsicWidth;
  final ButtonType? type;
  final double borderRadius;
  final Widget? icon;
  final EdgeInsetsGeometry? margin;

  ///[textWidgetLength] for wrap an overflow text if needed.
  final double? textWidgetLength;

  const AppButton(
      {super.key,
      this.backgroundColor,
      this.title,
      this.onSubmit,
      this.height = 54,
      this.width,
      this.titleStyle,
      this.titleSize = 16,
      this.isDisable = false,
      this.intrinsicWidth = false,
      this.borderRadius = 12,
      this.type = ButtonType.primary,
      this.titleColor,
      this.borderColor,
      this.textWidgetLength,
      this.margin,
      this.icon});

  ButtonStyle getStyle() {
    late ButtonStyle style;
    switch (type) {
      case ButtonType.primary:
        style = ButtonStyle(
          bg: backgroundColor ?? Colors.blue,
          titleColor: titleColor ?? Colors.white,
        );
        break;
      case ButtonType.secondary:
        style = ButtonStyle(
          bg: backgroundColor ?? Colors.grey,
          titleColor: titleColor ?? Colors.white,
        );
        break;
      case ButtonType.outline:
        style = ButtonStyle(
          bg: backgroundColor ?? Colors.transparent,
          titleColor: titleColor ?? Colors.blue,
        );
        break;
      default:
        break;
    }
    return style;
  }

  Widget _buildBT(BuildContext context) {
    ButtonStyle style = getStyle();

    return MotionButton(
      scale: 0.9,
      onTap: () {
        if (onSubmit != null && !isDisable) {
          onSubmit!();
        }
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Opacity(
        opacity: isDisable ? .5 : 1,
        child: Container(
          height: height,
          width: width,
          margin: margin,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: type == ButtonType.outline
                ? Border.all(width: 1, color: borderColor ?? AppColors.primary)
                : null,
            color: style.bg,
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                icon != null
                    ? Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: icon,
                      )
                    : Container(),
                SizedBox(
                  width: textWidgetLength,
                  child: Text(
                    title ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: titleStyle ??
                        AppTextStyle.bold(titleSize)
                            .copyWith(color: style.titleColor),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return intrinsicWidth
        ? IntrinsicWidth(
            child: _buildBT(context),
          )
        : _buildBT(context);
  }
}

class MotionButton extends StatefulWidget {
  final Widget child;
  final Function()? onTap;
  final double scale;
  final bool hideTooltip;
  final bool isDisable;
  const MotionButton(
      {super.key,
      required this.child,
      this.onTap,
      this.hideTooltip = true,
      this.isDisable = false,
      this.scale = 0.7});

  @override
  MotionButtonState createState() => MotionButtonState();
}

class MotionButtonState extends State<MotionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _scaleAnimation =
        Tween<double>(begin: 1, end: widget.scale).animate(_controller);
    _opacityAnimation = Tween<double>(begin: 1, end: 0.5).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTap: _onTap,
              child: Container(color: Colors.transparent, child: widget.child),
            ),
          ),
        );
      },
    );
  }

  Future<void> _onTap() async {
    if (widget.isDisable) return;
    _controller.forward();
    await Future.delayed(const Duration(milliseconds: 100));
    _controller.reverse();
    await Future.delayed(const Duration(milliseconds: 100));
    widget.onTap?.call();
  }
}
