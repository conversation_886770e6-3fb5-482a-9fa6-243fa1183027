import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiConstants {
  static String baseUrl = dotenv.env['baseUrl'] ?? "http://45.76.152.104:8080/api/v1/";
  static const String apiKey = 'api_key=06060606060606060606060606060606';
  static const String imageBaseUrl = 'https://image.tmdb.org/t/p/w500';
  static const int connectTimeout = 60000; // 60 seconds
  static const int receiveTimeout = 60000; // 60 seconds
  static const int codeSuccess = 200;
  static const int codeAuthenticationFailed = 401;
  static const int codeServerError = 500;
  static const int codeForbidden = 403;

  ///LOGIN
  static const String loginMobile = 'auth/login-app-mobile';
  static const String checkAccountExist = 'auth/is-username-exist';
  static const String verifyCapcha = 'auth/verify-capcha';

  ///OTP
  static const String verifyOtp = 'auth/verify-otp';
}
