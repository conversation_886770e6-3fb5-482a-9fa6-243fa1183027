class AppLanguage {
  AppLanguage._();
  static const String errorDefault = 'Something went wrong';
  static const String phoneNumber = 'Phone Number';
  static const String enterPhoneNumber = 'Enter your phone number';
  static const String password = 'Password';
  static const String enterPassword = 'Enter your password';
  static const String login = 'Login';
  static const String logout = 'Logout';
  static const String rememberMe = 'Remember me';
  static const String continueText = 'Continue';
  static const String contactCustomerCare = "Contact customer care";
  static const String verifyCapcha = "Plese verify captcha";
  static const String invalidPhoneNumber = "Invalid phone number";
  static const String tooManyRequests =
      "Too many requests. Please try again later or contact support.";
  static const String verificationFailed = "Verification failed";
  static const String resendOTP = "Resend OTP";
  static const String enterOTP = "Enter OTP";
  static const String verifyOTP = "Verify OTP";
  static const String verifyOTPSendToPhone = "Verify OTP sent to phone number";
  static const String didNotReceiveOTP = "Didn't receive OTP?";
  static const String resend = "Resend";
  static const String seconds = "seconds";
  static const String invalidOtpCode = 'invalidOtpCode';
  static const String sessionExpired = 'sessionExpired';
  static const String invalidVerificationId = 'invalidVerificationId';
  static const String networkError = 'networkError';
  static const String unknownError = 'unknownError';
  static const String invalidPhoneNumberError = 'invalidPhoneNumberError';
  static const String tooManyRequestsError = 'tooManyRequestsError';
  static const String verificationFailedError = 'verificationFailedError';
  static const String enterPoint = "enterPoint";
  static const String enterChooseUser = "enterChooseUser";
  static const String enterEmail = "enterEmail";
  static const String enterPhone = "enterPhone";
  static const String invalidEmail = "invalidEmail";
  static const String enterValidPhone = "enterValidPhone";
  static const String invalidPoint = "invalidPoint";
  static const String pointMustBePositive = "pointMustBePositive";
  static const String onlyAcceptSpecificValues = "onlyAcceptSpecificValues";
  static const String pointCannotExceedMaximum = "pointCannotExceedMaximum";
}
