import 'app_language.dart';

Map<String, String> enLanguage = {
  AppLanguage.errorDefault: 'Something went wrong',
  AppLanguage.phoneNumber: 'Phone Number',
  AppLanguage.enterPhoneNumber: 'Enter your phone number',
  AppLanguage.password: 'Password',
  AppLanguage.enterPassword: 'Enter your password',
  AppLanguage.login: 'Login',
  AppLanguage.logout: 'Logout',
  AppLanguage.rememberMe: 'Remember me',
  AppLanguage.continueText: 'Continue',
  AppLanguage.contactCustomerCare: "Contact customer care",
  AppLanguage.verifyCapcha: "Plese verify captcha",
  AppLanguage.invalidPhoneNumber: "Invalid phone number",
  AppLanguage.tooManyRequests:
      "Too many requests. Please try again later or contact support.",
  AppLanguage.verificationFailed: "Verification failed",
  AppLanguage.resendOTP: "Resend OTP",
  AppLanguage.enterOTP: "Enter OTP",
  AppLanguage.verifyOTP: "Verify OTP",
  AppLanguage.verifyOTPSendToPhone: "Verify OTP sent to phone number",
  AppLanguage.didNotReceiveOTP: "Didn't receive OTP?",
  AppLanguage.resend: "Resend",
  AppLanguage.seconds: "seconds",
  AppLanguage.invalidOtpCode: 'Invalid OTP code. Please try again.',
  AppLanguage.sessionExpired: 'Authentication session has expired. Please request a new OTP.',
  AppLanguage.invalidVerificationId: 'Invalid or expired verification code. Please try again.',
  AppLanguage.networkError: 'Network error. Please check your internet connection.',
  AppLanguage.unknownError: 'An unknown error occurred',
  AppLanguage.invalidPhoneNumberError: 'Invalid phone number',
  AppLanguage.tooManyRequestsError: 'Too many requests, please try again later',
  AppLanguage.verificationFailedError: 'Verification failed',
  AppLanguage.enterPoint: "Please enter point!",
  AppLanguage.enterChooseUser: "Please choose user!",
  AppLanguage.enterEmail: "Please enter email!",
  AppLanguage.enterPhone: "Please enter phone!",
  AppLanguage.invalidEmail: "Invalid email!",
  AppLanguage.enterValidPhone: "Please enter valid phone number!",
  AppLanguage.invalidPoint: "Invalid point!",
  AppLanguage.pointMustBePositive: "Point must be positive!",
  AppLanguage.onlyAcceptSpecificValues: "Only accept 0, 0.25, 0.5, 0.75...",
  AppLanguage.pointCannotExceedMaximum: "Point cannot exceed maximum @point point",
};
