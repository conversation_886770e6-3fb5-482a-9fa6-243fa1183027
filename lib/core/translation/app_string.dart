import 'package:base_app/core/extension/app_extension_string.dart';
import 'package:base_app/core/translation/app_language.dart';

class AppString {
  AppString._();
  static String get defaultError => AppLanguage.errorDefault.tr;
  static String get phoneNumber => AppLanguage.phoneNumber.tr;
  static String get enterPhoneNumber => AppLanguage.enterPhoneNumber.tr;
  static String get password => AppLanguage.password.tr;
  static String get enterPassword => AppLanguage.enterPassword.tr;
  static String get login => AppLanguage.login.tr;
  static String get logout => AppLanguage.logout.tr;
  static String get rememberMe => AppLanguage.rememberMe.tr;
  static String get continueText => AppLanguage.continueText.tr;
  static String get contactCustomerCare => AppLanguage.contactCustomerCare.tr;
  static String get verifyCapcha => AppLanguage.verifyCapcha.tr;
  static String get invalidPhoneNumber => AppLanguage.invalidPhoneNumber.tr;
  static String get tooManyRequests => AppLanguage.tooManyRequests.tr;
  static String get verificationFailed => AppLanguage.verificationFailed.tr;
  static String get resendOTP => AppLanguage.resendOTP.tr;
  static String get enterOTP => AppLanguage.enterOTP.tr;
  static String get verifyOTP => AppLanguage.verifyOTP.tr;
  static String get verifyOTPSendToPhone => AppLanguage.verifyOTPSendToPhone.tr;
  static String get didNotReceiveOTP => AppLanguage.didNotReceiveOTP.tr;
  static String get resend => AppLanguage.resend.tr;
  static String get seconds => AppLanguage.seconds.tr;
  static String get invalidOtpCode => AppLanguage.invalidOtpCode.tr;
  static String get sessionExpired => AppLanguage.sessionExpired.tr;
  static String get invalidVerificationId =>
      AppLanguage.invalidVerificationId.tr;
  static String get networkError => AppLanguage.networkError.tr;
  static String get unknownError => AppLanguage.unknownError.tr;
  static String get invalidPhoneNumberError =>
      AppLanguage.invalidPhoneNumberError.tr;
  static String get tooManyRequestsError => AppLanguage.tooManyRequestsError.tr;
  static String get verificationFailedError =>
      AppLanguage.verificationFailedError.tr;
  static String get enterPoint => AppLanguage.enterPoint.tr;
  static String get enterChooseUser => AppLanguage.enterChooseUser.tr;
  static String get enterEmail => AppLanguage.enterEmail.tr;
  static String get enterPhone => AppLanguage.enterPhone.tr;
  static String get invalidEmail => AppLanguage.invalidEmail.tr;
  static String get enterValidPhone => AppLanguage.enterValidPhone.tr;
  static String get invalidPoint => AppLanguage.invalidPoint.tr;
  static String get pointMustBePositive => AppLanguage.pointMustBePositive.tr;
  static String get onlyAcceptSpecificValues =>
      AppLanguage.onlyAcceptSpecificValues.tr;
  static String pointCannotExceedMaximum(String maxPoint) =>
      AppLanguage.pointCannotExceedMaximum.trParams({'point': maxPoint});
}
