import 'dart:ui';

import 'package:base_app/core/config/app_config.dart';
import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/local_storage/local_storage.dart';
import 'package:base_app/core/network/dio_client.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localization/flutter_localization.dart';

import 'app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    debugPrint('Flutter Error: ${details.exception}');
    debugPrint('Stack trace: ${details.stack}');
  };

  PlatformDispatcher.instance.onError = (error, stack) {
    debugPrint('Uncaught Error: $error');
    debugPrint('Stack trace: $stack');
    return true;
  };

  await FlutterLocalization.instance.ensureInitialized();
  await LocalStorage.instant.init();
  await configureDependencies();
  await AppConfig.initialConfig();
  final flavor = String.fromEnvironment(AppConfig.env.name);
  await dotenv.load(fileName: flavor == "dev" ? '.env.dev' : '.env.prod');
  getIt<DioClient>().init();

  runApp(const App());
}
