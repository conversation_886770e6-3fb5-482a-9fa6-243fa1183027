import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/features/login/data/models/capcha_request.dart';
import 'package:base_app/features/login/data/models/login_request.dart';
import 'package:base_app/features/login/data/models/user_model.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'login_remote_datasource.g.dart';

@RestApi()
@LazySingleton()
abstract class LoginRemoteDataSource {
  @factoryMethod
  factory LoginRemoteDataSource(Dio dio) = _LoginRemoteDataSource;

  @POST(ApiConstants.loginMobile)
  Future<UserModel> loginMobile({
    @Body() required LoginRequest request,
  });

  @GET(ApiConstants.checkAccountExist)
  Future<dynamic> checkAccountExist({
    @Query('username') required String phoneNumber,
  });

  @POST(ApiConstants.verifyCapcha)
  Future<bool> verifyCapcha({
    @Body() required CapchaRequest request,
  });
}
