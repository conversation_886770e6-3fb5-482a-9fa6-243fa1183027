import 'package:freezed_annotation/freezed_annotation.dart';

import 'roles_model.dart';

part 'user_model.g.dart';

@JsonSerializable(explicitToJson: true)
class UserModel {
  final String? id;
  final String? username;
  final String? fullName;
  final String? createdTime;
  final bool? active;
  final RolesModel? roles;

  UserModel({
    this.id,
    this.username,
    this.fullName,
    this.createdTime,
    this.active,
    this.roles,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}

