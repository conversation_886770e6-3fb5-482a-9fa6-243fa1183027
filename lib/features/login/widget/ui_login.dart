import 'package:base_app/core/constants/app_constants.dart';
import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/extension/app_extension_num.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/validate_util.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_input.dart';
import 'package:base_app/features/login/cubit/login_cubit.dart';
import 'package:base_app/features/login/data/models/login_request.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'ui_remember_phone.dart';

class UILogin extends StatefulWidget {
  const UILogin({super.key});

  @override
  State<UILogin> createState() => _UILoginState();
}

class _UILoginState extends State<UILogin> {
  final Map<String, dynamic> _formData = {};
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController phoneCtrl = TextEditingController();
  late LoginCubit loginCubit;

  @override
  void initState() {
    initData();
    super.initState();
  }

  @override
  void dispose() {
    phoneCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBase(
      showAppBar: false,
      body: BlocBuilder<LoginCubit, LoginState>(
        bloc: loginCubit,
        builder: (context, state) {
          return Form(
            autovalidateMode: state.summited
                ? AutovalidateMode.always
                : AutovalidateMode.disabled,
            key: _formKey,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: context.appPadding),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    context.appBarHeight.spacingHeight,
                    Hero(
                      tag: AppConstants.tagHeroLogo,
                      child: Assets.icons.iconLogo.image(
                        width: context.width / 1.5,
                      ),
                    ),
                    BaseInput(
                      controller: phoneCtrl,
                      label: AppString.phoneNumber,
                      hintText: AppString.enterPhoneNumber,
                      keyboardType: TextInputType.number,
                      isRequired: false,
                      submitted: state.summited,
                      onChanged: (String value) {
                        loginCubit.checkPhoneNumberSaved(phone: value);
                        _formData['username'] = value;
                      },
                      validator: (value) =>
                          ValidateUtil.instance.validateRequiredField(
                        value: value,
                        isRequired: true,
                        type: ValidationType.phone,
                      ),
                    ),
                    10.spacingHeight,
                    if (state.isAccountExisted == true)
                      BaseInput(
                        submitted: state.summited,
                        label: AppString.password,
                        hintText: AppString.enterPassword,
                        isRequired: false,
                        obscureText: state.obscureText,
                        suffixIcon: GestureDetector(
                            onTap: () {
                              loginCubit.showOrHidePassword(state.obscureText);
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(10),
                              child: state.obscureText
                                  ? Assets.icons.iconHide.image(height: 24)
                                  : Assets.icons.iconView.image(height: 24),
                            )),
                        onChanged: (String value) {
                          _formData['password'] = value;
                        },
                        validator: (value) =>
                            ValidateUtil.instance.validateRequiredField(
                          value: value,
                          isRequired: true,
                          type: ValidationType.password,
                        ),
                      ),
                    if (state.isAccountExisted == true) 10.spacingHeight,
                    UiRememberPhone(
                      rememberPhoneValue: state.isRememberPhone,
                      onChangedRemember: (value) =>
                          loginCubit.onChangeRemember(value),
                    ),
                    10.spacingHeight,
                    AppButton(
                      onSubmit: () => onSubmit(state.isAccountExisted),
                      title: state.isAccountExisted == true
                          ? AppString.login
                          : AppString.continueText,
                    ),
                    (context.width / 3).spacingHeight,
                    10.spacingHeight,
                    MotionButton(
                      onTap: () => _resetForm(),
                      child: Text(
                        AppString.contactCustomerCare,
                        style: AppTextStyle.medium(14)
                            .copyWith(decoration: TextDecoration.underline),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void initData() {
    loginCubit = getIt<LoginCubit>();
    rememberPhone();
  }

  void rememberPhone() async {
    final isRememberPhone = await loginCubit.getRememberPhoneLocal();
    final state = loginCubit.state;
    if (isRememberPhone) {
      phoneCtrl.text = state.phoneRemember ?? '';
      _formData['username'] = phoneCtrl.text;
    }
  }

  void _resetForm() {
    loginCubit.setSummited(false);
    _formKey.currentState?.reset();
    _formData.clear();
  }

  void onSubmit(bool? isAccountExisted) {
    loginCubit.setSummited(true);
    final bool isValid = _formKey.currentState?.validate() ?? false;

    if (isValid) {
      if (isAccountExisted == true) {
        _formKey.currentState?.save();
        loginCubit.login(
          request: LoginRequest(
            username: _formData['username'],
            password: _formData['password'],
          ),
        );
      } else {
        loginCubit.resetVerificationState();
        loginCubit.checkAccountExist(
          phone: _formData['username'],
        );
      }
    }
  }
}
