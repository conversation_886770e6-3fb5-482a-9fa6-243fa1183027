import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:flutter/material.dart';

class UiRememberPhone extends StatelessWidget {
  final bool rememberPhoneValue;
  final Function(bool?)? onChangedRemember;
  const UiRememberPhone({
    super.key,
    this.rememberPhoneValue = false,
    this.onChangedRemember,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onChangedRemember?.call(!rememberPhoneValue);
      },
      child: Row(
        children: [
          Checkbox(
            activeColor: AppColors.honoluluBlue,
            visualDensity: const VisualDensity(
              vertical: VisualDensity.minimumDensity,
              horizontal: VisualDensity.minimumDensity,
            ),
            value: rememberPhoneValue,
            onChanged: onChangedRemember,
          ),
          Text(
            AppString.rememberMe,
            style: AppTextStyle.regular(),
          )
        ],
      ),
    );
  }
}
