import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/firebase_auth_error_code.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/loading_overlay_util.dart';
import 'package:base_app/core/utils/string_util.dart';
import 'package:base_app/core/widgets/app_snack_bar.dart';
import 'package:base_app/features/login/cubit/login_cubit.dart';
import 'package:base_app/features/login/widget/ui_login.dart';
import 'package:base_app/features/otp/cubit/otp_cubit.dart';
import 'package:base_app/router/app_router.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late final OtpCubit _otpCubit;
  late final LoginCubit _loginCubit;

  @override
  void initState() {
    super.initState();
    _otpCubit = getIt<OtpCubit>();
    _loginCubit = getIt<LoginCubit>();
  }

  @override
  void dispose() {
    _otpCubit.close();
    _loginCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<LoginCubit, LoginState>(
          bloc: _loginCubit,
          listener: _handleLoginState,
        ),
        BlocListener<OtpCubit, OtpState>(
          bloc: _otpCubit,
          listener: _handleOtpState,
        ),
      ],
      child: const UILogin(),
    );
  }

  void _handleLoginState(BuildContext context, LoginState state) {
    if (_isLoading(state)) {
      AppLoadingOverlay.show(context);
      return;
    }

    AppLoadingOverlay.hide();

    if (_isSuccess(state)) {
      return;
    }

    if (state.successRecaptcha == false) {
      _showRecaptchaWarning(context);
    } else if (_shouldNavigateToSignup(state)) {
      _navigateToSignup(state);
    } else if (_shouldVerifyPhone(state)) {
      _verifyPhoneNumber(context, state);
    } else if (_isFailure(state)) {
      _handleLoginFailure(context, state);
    }
  }

  bool _isLoading(LoginState state) {
    return state.loginStatus == LoginStatus.loading ||
        state.checkAccountExistStatus == CheckAccountExistStatus.loading ||
        state.verifyCapchaStatus == VerifyCapchaStatus.loading;
  }

  bool _isSuccess(LoginState state) {
    return state.loginStatus == LoginStatus.success ||
        state.checkAccountExistStatus == CheckAccountExistStatus.success;
  }

  bool _isFailure(LoginState state) {
    return state.loginStatus == LoginStatus.failure ||
        state.checkAccountExistStatus == CheckAccountExistStatus.failure;
  }

  bool _shouldNavigateToSignup(LoginState state) {
    return state.verifyCapchaStatus == VerifyCapchaStatus.failure &&
        state.textErr == ApiConstants.codeForbidden.toString();
  }

  bool _shouldVerifyPhone(LoginState state) {
    return state.checkAccountExistStatus == CheckAccountExistStatus.success &&
        state.isAccountExisted == false &&
        state.successRecaptcha == true &&
        state.verifyCapchaStatus == VerifyCapchaStatus.success;
  }

  void _showRecaptchaWarning(BuildContext context) {
    AppSnackBar.warning(
      context,
      message: AppString.verifyCapcha,
    );
  }

  void _navigateToSignup(LoginState state) {
    // Get.to(() => SignUpScreen(userName: state.userName));
  }

  void _handleLoginFailure(BuildContext context, LoginState state) {
    if (state.textErr?.isNotEmpty ?? false) {
      AppSnackBar.error(
        context,
        message: state.textErr!,
        showInBottom: true,
      );
    }
    _loginCubit.resetLoginState();
  }

  Future<void> _verifyPhoneNumber(
    BuildContext context,
    LoginState state,
  ) async {
    try {
      await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: state.userName?.formatPhoneNumber,
        verificationCompleted: (_) => AppLoadingOverlay.hide(),
        verificationFailed: (e) => _onVerificationFailed(context, state, e),
        codeSent: (verificationId, resendToken) =>
            _onCodeSent(context, verificationId, resendToken),
        codeAutoRetrievalTimeout: (_) => AppLoadingOverlay.hide(),
      );
    } catch (e) {
      AppLoadingOverlay.hide();
    }
  }

  void _onVerificationFailed(
    BuildContext context,
    LoginState state,
    FirebaseAuthException exception,
  ) {
    AppLoadingOverlay.hide();

    final errorCode = FirebaseAuthErrorCode.values.firstWhere(
      (e) => e.code == exception.code,
      orElse: () => FirebaseAuthErrorCode.unknown,
    );

    final errorMessage = switch (errorCode) {
      FirebaseAuthErrorCode.invalidPhoneNumber => AppString.invalidPhoneNumber,
      FirebaseAuthErrorCode.tooManyRequests => AppString.tooManyRequests,
      FirebaseAuthErrorCode.webContextCancelled => null,
      FirebaseAuthErrorCode.invalidVerificationCode => AppString.invalidOtpCode,
      FirebaseAuthErrorCode.sessionExpired => AppString.sessionExpired,
      FirebaseAuthErrorCode.invalidVerificationId =>
        AppString.invalidVerificationId,
      FirebaseAuthErrorCode.networkRequestFailed => AppString.networkError,
      FirebaseAuthErrorCode.unknown =>
        '${AppString.verificationFailed} ${exception.message}',
    };

    if (errorMessage != null) {
      if (errorCode == FirebaseAuthErrorCode.webContextCancelled) {
        // Get.to(() => SignUpScreen(userName: state.userName));
      } else {
        AppSnackBar.error(
          context,
          message: errorMessage,
        );
      }
    }
  }

  void _onCodeSent(
    BuildContext context,
    String verificationId,
    int? resendToken,
  ) {
    AppLoadingOverlay.hide();
    context.go(
      AppRouter.otp,
      extra: {
        'verificationId': verificationId,
        'resendToken': resendToken,
      },
    );
  }

  void _handleOtpState(BuildContext context, OtpState state) {
    switch (state.otpStatus) {
      case OtpStatus.loading:
        AppLoadingOverlay.show(context);
      case OtpStatus.failure:
        AppLoadingOverlay.hide();
        AppSnackBar.error(context, message: state.errorMessage);
      case OtpStatus.success:
        AppLoadingOverlay.hide();
      default:
        break;
    }
  }
}
