import 'package:base_app/core/utils/task_either_util.dart';
import 'package:base_app/features/login/data/datasources/login_remote_datasource.dart';
import 'package:base_app/features/login/data/models/capcha_request.dart';
import 'package:base_app/features/login/data/models/login_request.dart';
import 'package:base_app/features/login/data/models/user_model.dart';
import 'package:injectable/injectable.dart';

@LazySingleton()
class LoginRepository {
  final LoginRemoteDataSource remoteDataSource;

  LoginRepository(this.remoteDataSource);

  AppTaskEither<UserModel> loginMobile({required LoginRequest request}) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.loginMobile(request: request),
    );
  }

  AppTaskEither<dynamic> checkAccountExist({required String phoneNumber}) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.checkAccountExist(phoneNumber: phoneNumber),
    );
  }

  AppTaskEither<bool> verifyCapcha({required CapchaRequest request}) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.verifyCapcha(request: request),
    );
  }
}
