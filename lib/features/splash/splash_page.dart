import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animScale;
  late Animation<double> _opacityAnim;

  @override
  void initState() {
    super.initState();

    _initAnimation();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      _animationController.forward();
    });
  }

  void _initAnimation() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: 1000,
      ),
    );
    _animScale = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.ease,
      ),
    );
    _opacityAnim = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.ease,
      ),
    );

    _animationController.addStatusListener(
      (status) async {
        if (status == AnimationStatus.completed) {
          await Future.delayed(
            const Duration(
              milliseconds: 500,
            ),
          );
          _navigateToMainApp();
        }
      },
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _navigateToMainApp() {
    if (!mounted) return;

    context.go(AppRouter.login);
  }

  @override
  Widget build(BuildContext context) {
    return AppBase(
      showAppBar: false,
      body: Stack(
        children: [
          FadeTransition(
            opacity: _opacityAnim,
            child: ScaleTransition(
              scale: _animScale,
              child: Center(
                child: Hero(
                  tag: 'LOGO',
                  child: Assets.icons.iconLogo.image(
                    width: context.width / 2,
                  ),
                ),
              ),
            ),
          ),
          // Debug button để test network
          Positioned(
            bottom: 50,
            right: 20,
            child: FloatingActionButton(
              mini: true,
              onPressed: () {
                context.go(AppRouter.networkTest);
              },
              child: const Icon(Icons.network_check),
            ),
          ),
        ],
      ),
    );
  }
}
