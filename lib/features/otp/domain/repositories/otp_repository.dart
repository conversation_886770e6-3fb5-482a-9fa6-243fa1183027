import 'package:base_app/core/utils/task_either_util.dart';
import 'package:base_app/features/otp/data/datasources/otp_remote_datasource.dart';
import 'package:base_app/features/otp/data/models/verify_otp_request.dart';
import 'package:injectable/injectable.dart';

@LazySingleton()
class OtpRepository {
  final OtpRemoteDataSource remoteDataSource;

  OtpRepository(this.remoteDataSource);

  AppTaskEither<String> verifyOtp({required VerifyOtpRequest request}) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.verifyOtp(request: request),
    );
  }
}
