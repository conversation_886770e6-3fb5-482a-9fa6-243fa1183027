import 'dart:async';
import 'dart:io';

import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/firebase_auth_error_code.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/string_util.dart';
import 'package:base_app/features/otp/data/models/verify_otp_request.dart';
import 'package:base_app/features/otp/domain/repositories/otp_repository.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

part 'otp_state.dart';

@injectable
@factoryMethod
class OtpCubit extends Cubit<OtpState> {
  OtpCubit() : super(OtpState());
  ILocalStorageService localStoreService = getIt<ILocalStorageService>();
  TextEditingController? otpController = TextEditingController();
  OtpRepository repository = getIt<OtpRepository>();
  Timer? _timer;

  void init(String verificationId, int? resendToken) async {
    String phone = await localStoreService.getUserPhone();
    emit(
      state.copyWith(
        currPhone: phone,
        verificationId: verificationId,
        resendToken: resendToken,
      ),
    );
  }

  /// Start the countdown timer
  void startCountdown(int seconds) {
    // Disable the resend button
    emit(state.copyWith(remainingSeconds: seconds, canResend: false));

    // Cancel any existing timer
    _timer?.cancel();

    // Start a new timer
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.remainingSeconds > 0) {
        emit(state.copyWith(remainingSeconds: state.remainingSeconds - 1));
      } else {
        timer.cancel();
        emit(state.copyWith(remainingSeconds: 0, canResend: true));
      }
    });
  }

  /// Clean up the timer when not needed
  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }

  Future<void> signInWithPhoneAuthCredential(
      PhoneAuthCredential credential) async {
    try {
      emit(state.copyWith(otpStatus: OtpStatus.loading));

      UserCredential userCredential =
          await FirebaseAuth.instance.signInWithCredential(credential);

      String accessToken = await userCredential.user?.getIdToken() ?? '';
      if (StringUtils.isNotNullOrEmpty(accessToken)) {
        final response = await repository
            .verifyOtp(
                request: VerifyOtpRequest(
                    phone: state.currPhone, idToken: accessToken))
            .run();
        response.fold(
          (failure) {
            emit(state.copyWith(otpStatus: OtpStatus.failure));
          },
          (success) {
            emit(state.copyWith(otpStatus: OtpStatus.success));
          },
        );
      } else {
        emit(state.copyWith(otpStatus: OtpStatus.failure));
      }
    } on FirebaseAuthException catch (e) {
      final errorCode = FirebaseAuthErrorCode.fromCode(e.code);
      emit(state.copyWith(
        otpStatus: OtpStatus.failure,
        firebaseErrorCode: errorCode,
        firebaseErrorMessage: e.message,
      ));
    } on DioException catch (e) {
      emit(
        state.copyWith(
          otpStatus: OtpStatus.failure,
          errorMessage: e.response?.data['message'][0]['msg'].toString() ??
              AppString.unknownError,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          otpStatus: OtpStatus.failure,
          errorMessage: AppString.unknownError,
        ),
      );
    }
  }

  void verifyOTP(String otp) async {
    try {
      emit(state.copyWith(otpStatus: OtpStatus.loading, errorMessage: ''));
      // Create the PhoneAuthCredential
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: state.verificationId ?? '',
        smsCode: otp,
      );
      await signInWithPhoneAuthCredential(credential);
    } on FirebaseAuthException catch (e) {
      emit(
        state.copyWith(
          otpStatus: OtpStatus.failure,
          errorMessage: e.message ?? AppString.defaultError,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          otpStatus: OtpStatus.failure,
          errorMessage: AppString.defaultError,
        ),
      );
    }
  }

  void resend() async {
    otpController?.text = "";
    emit(state.copyWith(otpStatus: OtpStatus.loading, errorMessage: ''));
    await FirebaseAuth.instance.verifyPhoneNumber(
      phoneNumber: state.currPhone.formatPhoneNumber,
      forceResendingToken: state.resendToken,
      verificationCompleted: (PhoneAuthCredential credential) async {
        if (Platform.isAndroid) {
          await signInWithPhoneAuthCredential(credential);
        }
      },
      verificationFailed: (FirebaseAuthException e) {
        final errorCode = FirebaseAuthErrorCode.fromCode(e.code);
        emit(
          state.copyWith(
            otpStatus: OtpStatus.failure,
            resendOTPStatus: ResendOTPStatus.failure,
            firebaseErrorCode: errorCode,
            firebaseErrorMessage: e.message,
          ),
        );
      },
      codeSent: (String verificationId, int? resendToken) {
        emit(state.copyWith(
            otpStatus: OtpStatus.initial,
            verificationId: verificationId,
            resendToken: resendToken));
      },
      codeAutoRetrievalTimeout: (String verificationId) {
        emit(state.copyWith(otpStatus: OtpStatus.failure));
      },
    );
  }
}
