part of 'otp_cubit.dart';

enum OtpStatus { initial, loading, success, failure }

enum VerifyOTPStatus { initial, loading, success, failure }

enum ResendOTPStatus { initial, loading, success, failure }


class OtpState {
  final OtpStatus otpStatus;
  final VerifyOTPStatus verifyOTPStatus;
  final ResendOTPStatus resendOTPStatus;
  final String currPhone;
  final String? verificationId;
  final int? resendToken;
  final int remainingSeconds;
  final bool canResend;
  final String errorMessage;
  final FirebaseAuthErrorCode? firebaseErrorCode;
  final String? firebaseErrorMessage;

  OtpState({
    this.otpStatus = OtpStatus.initial,
    this.verifyOTPStatus = VerifyOTPStatus.initial,
    this.resendOTPStatus = ResendOTPStatus.initial,
    this.currPhone = '',
    this.verificationId,
    this.resendToken,
    this.errorMessage = '',
    this.remainingSeconds = 0,
    this.canResend = true,
    this.firebaseErrorCode,
    this.firebaseErrorMessage,
  });

  OtpState copyWith({
    OtpStatus? otpStatus,
    VerifyOTPStatus? verifyOTPStatus,
    ResendOTPStatus? resendOTPStatus,
    String? currPhone,
    String? verificationId,
    int? resendToken,
    String? errorMessage,
    int? remainingSeconds,
    bool? canResend,
    FirebaseAuthErrorCode? firebaseErrorCode,
    String? firebaseErrorMessage,
  }) {
    return OtpState(
      otpStatus: otpStatus ?? this.otpStatus,
      verifyOTPStatus: verifyOTPStatus ?? this.verifyOTPStatus,
      resendOTPStatus: resendOTPStatus ?? this.resendOTPStatus,
      currPhone: currPhone ?? this.currPhone,
      verificationId: verificationId ?? this.verificationId,
      resendToken: resendToken ?? this.resendToken,
      errorMessage: errorMessage ?? this.errorMessage,
      remainingSeconds: remainingSeconds ?? this.remainingSeconds,
      canResend: canResend ?? this.canResend,
      firebaseErrorCode: firebaseErrorCode ?? this.firebaseErrorCode,
      firebaseErrorMessage: firebaseErrorMessage ?? this.firebaseErrorMessage,
    );
  }
}
