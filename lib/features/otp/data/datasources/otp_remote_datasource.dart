import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/features/otp/data/models/verify_otp_request.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'otp_remote_datasource.g.dart';

@RestApi()
@LazySingleton()
abstract class OtpRemoteDataSource {
  @factoryMethod
  factory OtpRemoteDataSource(Dio dio) = _OtpRemoteDataSource;

  @POST(ApiConstants.verifyOtp)
  Future<String> verifyOtp({
    @Body() required VerifyOtpRequest request,
  });
}
