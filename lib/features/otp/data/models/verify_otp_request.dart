import 'package:json_annotation/json_annotation.dart';

part 'verify_otp_request.g.dart';

@JsonSerializable()
class VerifyOtpRequest {
  final String phone;
  final String idToken;

  VerifyOtpRequest({
    required this.phone,
    required this.idToken,
  });

  // Factory constructor for parsing JSON
  factory VerifyOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifyOtpRequestFromJson(json);

  // Method to convert the model to JSON
  Map<String, dynamic> toJson() => _$VerifyOtpRequestToJson(this);
}
