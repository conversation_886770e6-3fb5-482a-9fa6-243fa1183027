import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/extension/app_extension_num.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/loading_overlay_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_snack_bar.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';
import 'package:base_app/core/enums/firebase_auth_error_code.dart';

import 'cubit/otp_cubit.dart';

class OTPScreen extends StatefulWidget {
  const OTPScreen({
    super.key,
    required this.verificationId,
    this.resendToken,
    this.userName,
  });

  final String verificationId;
  final int? resendToken;
  final String? userName;

  @override
  State<OTPScreen> createState() => _OTPScreenState();
}

class _OTPScreenState extends State<OTPScreen> {
  late final OtpCubit _otpCubit;

  @override
  void initState() {
    super.initState();
    _otpCubit = getIt<OtpCubit>();
    _otpCubit.init(widget.verificationId, widget.resendToken);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OtpCubit, OtpState>(
      listener: _handleStateChanges,
      child: AppBase(
        centerTitle: false,
        titleAppBar: "",
        body: const _OTPBody(),
      ),
    );
  }

  void _handleStateChanges(BuildContext context, OtpState state) {
    switch (state.otpStatus) {
      case OtpStatus.loading:
        AppLoadingOverlay.show(context);
        break;
      case OtpStatus.success:
        AppLoadingOverlay.hide();
        break;
      case OtpStatus.failure:
        AppLoadingOverlay.hide();
        _showErrorMessage(context, state);
        break;
      default:
        break;
    }

    if (state.resendOTPStatus == ResendOTPStatus.failure) {
      AppLoadingOverlay.hide();
      _showErrorMessage(context, state);
    }
  }

  void _showErrorMessage(BuildContext context, OtpState state) {
    final errorMessage = _getErrorMessage(state);
    AppSnackBar.error(context, message: errorMessage);
  }

  String _getErrorMessage(OtpState state) {
    if (state.firebaseErrorCode != null) {
      return switch (state.firebaseErrorCode!) {
        FirebaseAuthErrorCode.invalidVerificationCode =>
          AppString.invalidOtpCode,
        FirebaseAuthErrorCode.sessionExpired => AppString.sessionExpired,
        FirebaseAuthErrorCode.invalidVerificationId =>
          AppString.invalidVerificationId,
        FirebaseAuthErrorCode.networkRequestFailed => AppString.networkError,
        FirebaseAuthErrorCode.invalidPhoneNumber =>
          '${AppString.invalidPhoneNumberError} ${state.firebaseErrorMessage ?? ''}',
        FirebaseAuthErrorCode.tooManyRequests => AppString.tooManyRequestsError,
        FirebaseAuthErrorCode.unknown =>
          '${AppString.unknownError}: ${state.firebaseErrorMessage ?? ''}',
        _ =>
          '${AppString.verificationFailedError} ${state.firebaseErrorMessage ?? ''}',
      };
    }
    return state.errorMessage.isNotEmpty
        ? state.errorMessage
        : AppString.unknownError;
  }
}

class _OTPBody extends StatelessWidget {
  const _OTPBody();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildTitle(),
          12.spacingHeight,
          const _PhoneNumberDisplay(),
          32.spacingHeight,
          const _OTPInput(),
          28.spacingHeight,
          const _ResendSection(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      AppString.verifyOTP,
      style: AppTextStyle.bold(20.sp),
    );
  }
}

class _PhoneNumberDisplay extends StatelessWidget {
  const _PhoneNumberDisplay();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OtpCubit, OtpState>(
      builder: (context, state) {
        return RichText(
          text: TextSpan(
            text: "${AppString.verifyOTPSendToPhone} ",
            style: AppTextStyle.regular(14.sp),
            children: [
              TextSpan(
                text: state.currPhone,
                style: AppTextStyle.bold(14.sp).copyWith(
                  color: AppColors.vampireBlack,
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

class _OTPInput extends StatelessWidget {
  const _OTPInput();

  @override
  Widget build(BuildContext context) {
    final otpCubit = context.read<OtpCubit>();

    return Pinput(
      length: 6,
      controller: otpCubit.otpController,
      defaultPinTheme: _getDefaultPinTheme(),
      focusedPinTheme: _getFocusedPinTheme(),
      submittedPinTheme: _getSubmittedPinTheme(),
      pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
      showCursor: true,
      onCompleted: otpCubit.verifyOTP,
    );
  }

  PinTheme _getDefaultPinTheme() {
    return PinTheme(
      width: 40,
      height: 56,
      textStyle: AppTextStyle.regular(14.sp),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.brightGray),
        borderRadius: BorderRadius.circular(5.r),
      ),
    );
  }

  PinTheme _getFocusedPinTheme() {
    return _getDefaultPinTheme().copyDecorationWith(
      border: Border.all(color: AppColors.honoluluBlue),
      borderRadius: BorderRadius.circular(5.r),
    );
  }

  PinTheme _getSubmittedPinTheme() {
    return _getDefaultPinTheme().copyWith(
      decoration: _getDefaultPinTheme().decoration!.copyWith(
            color: AppColors.brightGray,
          ),
    );
  }
}

class _ResendSection extends StatelessWidget {
  const _ResendSection();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OtpCubit, OtpState>(
      builder: (context, state) {
        return RichText(
          text: TextSpan(
            text: '${AppString.didNotReceiveOTP} ',
            style: AppTextStyle.regular(12.sp).copyWith(
              color: AppColors.taupeGray,
            ),
            children: [
              if (state.remainingSeconds > 0)
                _buildCountdownText(state)
              else
                _buildResendButton(context, state),
            ],
          ),
        );
      },
    );
  }

  TextSpan _buildCountdownText(OtpState state) {
    return TextSpan(
      text:
          '${AppString.resend} (${state.remainingSeconds} ${AppString.seconds})',
      style: AppTextStyle.regular(14.sp).copyWith(
        color: AppColors.honoluluBlue,
      ),
    );
  }

  TextSpan _buildResendButton(BuildContext context, OtpState state) {
    return TextSpan(
      recognizer: TapGestureRecognizer()
        ..onTap = state.canResend ? () => _handleResend(context) : null,
      text: AppString.resendOTP,
      style: AppTextStyle.bold(14.sp).copyWith(
        color: AppColors.red,
      ),
    );
  }

  void _handleResend(BuildContext context) {
    final otpCubit = context.read<OtpCubit>();
    otpCubit.startCountdown(60);
    otpCubit.resend();
  }
}
