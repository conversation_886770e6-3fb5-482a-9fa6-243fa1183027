init: pub-get

clean:
	fvm flutter clean

pub-get:
	fvm flutter pub get

gen:
	dart run build_runner build --delete-conflicting-outputs

activate:
	dart pub global activate flutterfire_cli

pod-install:
	cd ios && rm -rf Pods && rm -rf build && rm -rf Podfile.lock && pod install && cd ..

run-release:
	fvm flutter run --release

run-profile:
	fvm flutter run --profile

apk:
	flutter build apk --release --no-tree-shake-icons

aab:
	flutter build appbundle --release --no-tree-shake-icons

run-dev:
	fvm flutter run --flavor dev

run-prod:
	fvm flutter run --flavor prod